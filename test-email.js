// Test script to verify email functionality
const { sendJobApplicationEmail } = require('./src/lib/email.ts');

async function testJobApplicationEmail() {
  const testData = {
    jobTitle: 'Test Position',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>', // This should receive the auto-reply
    phone: '+1234567890',
    location: 'Test Location',
    coverLetter: 'This is a test cover letter to verify the email functionality.',
  };

  console.log('Testing job application email...');
  console.log('Test data:', testData);
  
  try {
    const result = await sendJobApplicationEmail(testData);
    console.log('Result:', result);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testJobApplicationEmail();
